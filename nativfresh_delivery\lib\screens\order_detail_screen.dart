import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/order.dart' as order_model;
import '../services/order_service.dart';
import '../services/simple_notification_service.dart';
import 'payment_screen.dart';

class OrderDetailScreen extends StatefulWidget {
  final order_model.Order order;

  const OrderDetailScreen({super.key, required this.order});

  @override
  State<OrderDetailScreen> createState() => _OrderDetailScreenState();
}

class _OrderDetailScreenState extends State<OrderDetailScreen> {
  final OrderService _orderService = OrderService();
  bool _isUpdating = false;
  late order_model.Order _currentOrder;

  @override
  void initState() {
    super.initState();
    _currentOrder = widget.order;
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not make phone call'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showCancelOrderDialog() {
    final reasonController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Order'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Are you sure you want to cancel this entire order?'),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Cancellation Reason',
                hintText: 'e.g., Customer refused delivery, Address not found',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              final reason = reasonController.text.trim();
              if (reason.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Please provide a cancellation reason')),
                );
                return;
              }

              Navigator.of(context).pop();
              await _cancelOrder(reason);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Confirm Cancellation'),
          ),
        ],
      ),
    );
  }

  void _showPartialCancelDialog() {
    if (_currentOrder.items.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No items to cancel')),
      );
      return;
    }

    final selectedItems = List<bool>.filled(_currentOrder.items.length, false);
    final reasonController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Cancel Items'),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Select items to cancel:'),
                const SizedBox(height: 16),
                Flexible(
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: _currentOrder.items.length,
                    itemBuilder: (context, index) {
                      final item = _currentOrder.items[index];
                      return CheckboxListTile(
                        title: Text(item.productName),
                        subtitle: Text('Qty: ${item.quantity} × ₹${item.price.toStringAsFixed(2)}'),
                        value: selectedItems[index],
                        onChanged: (value) {
                          setState(() {
                            selectedItems[index] = value ?? false;
                          });
                        },
                      );
                    },
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: reasonController,
                  decoration: const InputDecoration(
                    labelText: 'Cancellation Reason',
                    hintText: 'e.g., Items not available, Customer request',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 2,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                final reason = reasonController.text.trim();
                final selectedIndices = <int>[];

                for (int i = 0; i < selectedItems.length; i++) {
                  if (selectedItems[i]) {
                    selectedIndices.add(i);
                  }
                }

                if (selectedIndices.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Please select at least one item to cancel')),
                  );
                  return;
                }

                if (reason.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Please provide a cancellation reason')),
                  );
                  return;
                }

                Navigator.of(context).pop();
                await _partialCancelOrder(selectedIndices, reason);
              },
              style: TextButton.styleFrom(foregroundColor: Colors.orange),
              child: const Text('Cancel Selected Items'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _cancelOrder(String reason) async {
    setState(() {
      _isUpdating = true;
    });

    try {
      await _orderService.cancelOrder(_currentOrder.id, reason);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Order cancelled successfully'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop(); // Go back to orders list
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to cancel order: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  Future<void> _partialCancelOrder(List<int> itemIndices, String reason) async {
    setState(() {
      _isUpdating = true;
    });

    try {
      await _orderService.partialCancelOrderByIndices(_currentOrder.id, itemIndices, reason);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Items cancelled successfully'),
            backgroundColor: Colors.green,
          ),
        );
        // Refresh the order data
        _loadOrderDetails();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to cancel items: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  void _loadOrderDetails() async {
    try {
      final order = await _orderService.getOrder(_currentOrder.id);
      if (order != null && mounted) {
        setState(() {
          _currentOrder = order;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to refresh order: $e')),
        );
      }
    }
  }

  Future<void> _updateOrderStatus(order_model.OrderStatus newStatus) async {
    setState(() {
      _isUpdating = true;
    });

    try {
      await _orderService.updateOrderStatus(_currentOrder.id, newStatus);

      // Update local order state
      setState(() {
        _currentOrder = _currentOrder.copyWith(status: newStatus);
      });

      // Show success notification
      if (newStatus == order_model.OrderStatus.delivered) {
        SimpleNotificationService().notifyDeliverySuccess(
          _currentOrder.orderNumber,
          _currentOrder.customerInfo.name,
        );
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Order status updated to ${_orderService.getStatusDisplayName(newStatus)}'),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate back if delivered
        if (newStatus == order_model.OrderStatus.delivered) {
          Navigator.pop(context);
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }

  void _showStatusChangeOptions() {
    final availableStatuses = _orderService.getAvailableStatusOptions(_currentOrder.status);

    if (availableStatuses.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No status changes available for this order'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Change Order Status',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              ...availableStatuses.map((status) => ListTile(
                leading: Icon(
                  status == order_model.OrderStatus.delivered
                    ? Icons.check_circle
                    : Icons.local_shipping,
                  color: status == order_model.OrderStatus.delivered
                    ? Colors.green
                    : Colors.orange,
                ),
                title: Text(_orderService.getStatusDisplayName(status)),
                onTap: () {
                  Navigator.pop(context);
                  _updateOrderStatus(status);
                },
              )).toList(),
            ],
          ),
        );
      },
    );
  }

  void _showPaymentOptions() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PaymentScreen(order: _currentOrder),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Order #${_currentOrder.orderNumber}'),
        backgroundColor: Colors.green.shade600,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showStatusChangeOptions,
            icon: const Icon(Icons.edit),
            tooltip: 'Change Status',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status Card
            Card(
              color: Colors.orange.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(
                      Icons.delivery_dining,
                      color: Colors.orange.shade700,
                      size: 32,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Status: ${_currentOrder.statusDisplayName}',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.orange.shade700,
                            ),
                          ),
                          Text(
                            'Assigned for delivery',
                            style: TextStyle(
                              color: Colors.orange.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Customer Information
            Text(
              'Customer Information',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.person, color: Colors.grey),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            _currentOrder.customerInfo.name.isNotEmpty
                                ? _currentOrder.customerInfo.name
                                : 'Customer Name Not Available',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: _currentOrder.customerInfo.name.isNotEmpty
                                  ? Colors.black
                                  : Colors.grey.shade600,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        const Icon(Icons.phone, color: Colors.grey),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            _currentOrder.customerInfo.phoneNumber.isNotEmpty
                                ? _currentOrder.customerInfo.phoneNumber
                                : 'Phone Number Not Available',
                            style: TextStyle(
                              fontSize: 16,
                              color: _currentOrder.customerInfo.phoneNumber.isNotEmpty
                                  ? Colors.black
                                  : Colors.grey.shade600,
                            ),
                          ),
                        ),
                        if (_currentOrder.customerInfo.phoneNumber.isNotEmpty)
                          IconButton(
                            onPressed: () => _makePhoneCall(_currentOrder.customerInfo.phoneNumber),
                            icon: const Icon(Icons.call),
                            color: Colors.green,
                            tooltip: 'Call Customer',
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Delivery Address
            Text(
              'Delivery Address',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    const Icon(Icons.location_on, color: Colors.grey),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        _currentOrder.deliveryAddress.fullAddress,
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Order Items
            Text(
              'Order Items',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    if (_currentOrder.items.isEmpty)
                      const Padding(
                        padding: EdgeInsets.all(16.0),
                        child: Text('No items found'),
                      )
                    else
                      ..._currentOrder.items.map((item) => Padding(
                        padding: const EdgeInsets.only(bottom: 12),
                        child: Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    item.productName.isNotEmpty
                                        ? item.productName
                                        : 'Product Name Not Available',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      color: item.productName.isNotEmpty
                                          ? Colors.black
                                          : Colors.grey.shade600,
                                    ),
                                  ),
                                  Text(
                                    item.price > 0
                                        ? 'Qty: ${item.quantity} × ₹${item.price.toStringAsFixed(2)}'
                                        : 'Qty: ${item.quantity} × Price Not Available',
                                    style: TextStyle(
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Text(
                              item.totalPrice > 0
                                  ? '₹${item.totalPrice.toStringAsFixed(2)}'
                                  : 'N/A',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: item.totalPrice > 0
                                    ? Colors.black
                                    : Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      )),
                    const Divider(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Total Amount',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          _currentOrder.totalAmount > 0
                              ? _currentOrder.formattedTotalAmount
                              : 'Amount Not Available',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: _currentOrder.totalAmount > 0
                                ? Colors.green
                                : Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 30),

            // Action Buttons
            Column(
              children: [
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton.icon(
                    onPressed: _showPaymentOptions,
                    icon: const Icon(Icons.payment, size: 24),
                    label: const Text(
                      'Collect Payment',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade600,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton.icon(
                    onPressed: _isUpdating ? null : () => _updateOrderStatus(order_model.OrderStatus.delivered),
                    icon: _isUpdating
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Icon(Icons.check_circle, size: 24),
                    label: Text(
                      _isUpdating ? 'Updating...' : 'Mark as Delivered',
                      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green.shade600,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Cancellation Options
                Text(
                  'Order Issues',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.red.shade700,
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: _isUpdating ? null : _showPartialCancelDialog,
                        icon: const Icon(Icons.remove_circle_outline),
                        label: const Text('Cancel Items'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.orange.shade700,
                          side: BorderSide(color: Colors.orange.shade700),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: _isUpdating ? null : _showCancelOrderDialog,
                        icon: const Icon(Icons.cancel_outlined),
                        label: const Text('Cancel Order'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.red.shade700,
                          side: BorderSide(color: Colors.red.shade700),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
